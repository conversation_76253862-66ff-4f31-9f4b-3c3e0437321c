'use client';

import { useState, useEffect } from 'react';

export default function Home() {
  const [agentState, setAgentState] = useState<any>(null);
  const [startingPoint, setStartingPoint] = useState('');
  const [extraRequest, setExtraRequest] = useState('');
  const [loading, setLoading] = useState(false);

  // Fetch agent state from API
  useEffect(() => {
    fetch('/api/agent')
      .then(res => res.json())
      .then(setAgentState);
  }, []);

  const handleStart = async () => {
    setLoading(true);
    const res = await fetch('/api/agent', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ startingPoint, extraRequest })
    });
    const data = await res.json();
    setAgentState(data);
    setLoading(false);
  };

  return (
    <div style={{ display: 'flex', minHeight: '100vh' }}>
      {/* Sidebar for headings */}
      <aside style={{ width: 250, background: '#ffff', padding: 24 }}>
        <h2>Sections</h2>
        <ul>
          {agentState?.headings?.map((h: any, i: number) => (
            <li key={i}><a href={`#${h.id}`}>{h.title}</a></li>
          ))}
        </ul>
      </aside>
      <main style={{ flex: 1, padding: 32 }}>
        <h1>Research Agent Dashboard</h1>
        <div style={{ marginBottom: 24 }}>
          <input
            type="text"
            placeholder="Enter starting point..."
            value={startingPoint}
            onChange={e => setStartingPoint(e.target.value)}
            style={{ marginRight: 8 }}
          />
          <input
            type="text"
            placeholder="Extra request (optional)"
            value={extraRequest}
            onChange={e => setExtraRequest(e.target.value)}
            style={{ marginRight: 8 }}
          />
          <button onClick={handleStart} disabled={loading}>
            {loading ? 'Processing...' : 'Start/Update'}
          </button>
        </div>
        <section style={{ marginBottom: 32 }}>
          <h2>Current Stage</h2>
          <div>{agentState?.stage || 'Not started'}</div>
        </section>
        <section style={{ marginBottom: 32 }}>
          <h2>Agent State</h2>
          <pre style={{ background: '#eee', padding: 16 }}>
            {JSON.stringify(agentState, null, 2)}
          </pre>
        </section>
        <section style={{ marginBottom: 32 }}>
          <h2>Chat History</h2>
          <div style={{ background: '#fafafa', padding: 16, minHeight: 80 }}>
            {agentState?.chat?.map((msg: any, i: number) => (
              <div key={i}><b>{msg.role}:</b> {msg.content}</div>
            )) || 'No chat yet.'}
          </div>
        </section>
        <section style={{ marginBottom: 32 }}>
          <h2>Research Context (Scratchpad)</h2>
          <div style={{ background: '#fafafa', padding: 16, minHeight: 80 }}>
            {agentState?.scratchpad || 'No context yet.'}
          </div>
        </section>
        <section style={{ marginBottom: 32 }}>
          <h2>Search List</h2>
          <ul>
            {agentState?.searchList?.map((item: string, i: number) => (
              <li key={i}>{item}</li>
            )) || <li>No search items yet.</li>}
          </ul>
        </section>
      </main>
    </div>
  );
}